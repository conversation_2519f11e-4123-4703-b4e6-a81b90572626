# Spring 环境动态切换示例

这个项目演示了如何在Spring Boot应用中实现运行时动态切换不同环境的配置文件。

## 功能特性

- ✅ 运行时动态切换配置文件（dev、prod、test）
- ✅ 使用 `@ConfigurationProperties` 自动绑定配置
- ✅ 支持 `@RefreshScope` 配置热刷新
- ✅ 线程安全的配置管理
- ✅ 配置变更事件通知
- ✅ REST API 接口管理
- ✅ 完整的示例和测试

## 项目结构

```
src/
├── main/
│   ├── java/com/example/
│   │   ├── Application.java                 # Spring Boot 启动类
│   │   ├── config/
│   │   │   ├── AppConfig.java              # 配置实体类
│   │   │   ├── DynamicConfigManager.java   # 动态配置管理器
│   │   │   └── EnvironmentChangeEvent.java # 环境切换事件
│   │   ├── controller/
│   │   │   ├── ConfigController.java       # 配置管理API
│   │   │   └── DemoController.java         # 演示API
│   │   └── service/
│   │       └── ConfigDemoService.java      # 配置演示服务
│   └── resources/
│       ├── application.properties          # 主配置文件
│       ├── config-dev.properties          # 开发环境配置
│       ├── config-prod.properties         # 生产环境配置
│       └── config-test.properties         # 测试环境配置
└── test/
    └── java/com/example/
        └── ApplicationTest.java            # 测试类
```

## 快速开始

### 1. 启动应用

```bash
# 使用Maven启动
mvn spring-boot:run

# 或者编译后运行
mvn clean package
java -jar target/spring-env-switch-1.0.0.jar
```

### 2. 访问应用

应用启动后，访问 http://localhost:8080

### 3. API 接口

#### 配置管理接口

- `GET /api/config/environment` - 获取当前环境信息
- `POST /api/config/environment/{env}` - 切换环境（dev/prod/test）
- `GET /api/config/current` - 获取当前配置详情
- `GET /api/config/properties` - 获取原始配置属性
- `GET /api/config/health` - 健康检查

#### 演示接口

- `GET /api/demo/database` - 演示数据库配置
- `GET /api/demo/redis` - 演示Redis配置
- `GET /api/demo/api` - 演示API配置
- `GET /api/demo/features` - 演示功能开关
- `GET /api/demo/notifications` - 演示通知配置
- `GET /api/demo/summary` - 获取完整配置摘要

## 使用示例

### 1. 查看当前环境

```bash
curl http://localhost:8080/api/config/environment
```

响应：
```json
{
  "currentEnvironment": "dev",
  "supportedEnvironments": ["dev", "prod", "test"],
  "timestamp": 1640995200000
}
```

### 2. 切换到生产环境

```bash
curl -X POST http://localhost:8080/api/config/environment/prod
```

响应：
```json
{
  "success": true,
  "currentEnvironment": "prod",
  "targetEnvironment": "prod",
  "message": "环境切换成功",
  "timestamp": 1640995200000
}
```

### 3. 查看当前配置

```bash
curl http://localhost:8080/api/config/current
```

### 4. 演示配置使用

```bash
curl http://localhost:8080/api/demo/summary
```

## 配置文件说明

### 开发环境 (config-dev.properties)
- 使用本地数据库和Redis
- 启用调试模式
- 禁用通知功能

### 生产环境 (config-prod.properties)
- 使用生产数据库和Redis
- 启用监控和通知
- 禁用调试模式

### 测试环境 (config-test.properties)
- 使用内存数据库
- 禁用缓存和通知
- 启用调试模式

## 核心实现原理

1. **动态配置源**: 使用 `PropertiesPropertySource` 动态添加配置源
2. **配置刷新**: 使用 `ContextRefresher` 刷新Spring上下文
3. **线程安全**: 使用 `ReentrantReadWriteLock` 保证并发安全
4. **事件通知**: 使用 `ApplicationEventPublisher` 发布配置变更事件
5. **自动绑定**: 使用 `@ConfigurationProperties` 自动绑定配置到实体

## 扩展功能

- 支持配置中心集成（如Nacos、Apollo）
- 支持配置加密/解密
- 支持配置版本管理
- 支持配置回滚功能
- 支持配置变更审计

## 注意事项

1. 配置切换是全局操作，会影响整个应用
2. 频繁切换配置可能影响性能
3. 建议在生产环境谨慎使用动态切换功能
4. 配置文件必须存在于classpath中

## 技术栈

- Spring Boot 3.2.0
- Spring Cloud Context
- Maven
- JUnit 5
