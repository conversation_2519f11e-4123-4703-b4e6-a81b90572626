package com.example.controller;

import com.example.config.ConfigProxy;
import com.example.config.ConfigScope;
import com.example.config.DynamicConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 作用域配置测试控制器
 * 演示临时配置和全局配置的区别
 */
@RestController
@RequestMapping("/api/scope-test")
public class ScopedConfigTestController {

    private static final Logger logger = LoggerFactory.getLogger(ScopedConfigTestController.class);

    private final DynamicConfigManager configManager;
    private final ConfigProxy configProxy;
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @Autowired
    public ScopedConfigTestController(DynamicConfigManager configManager, ConfigProxy configProxy) {
        this.configManager = configManager;
        this.configProxy = configProxy;
    }

    /**
     * 测试临时配置与全局配置的区别
     */
    @PostMapping("/compare-scopes/{targetEnv}")
    public ResponseEntity<Map<String, Object>> compareScopes(@PathVariable String targetEnv) {
        logger.info("开始测试临时配置与全局配置的区别: 目标环境 = {}", targetEnv);
        
        Map<String, Object> response = new HashMap<>();
        
        // 记录初始状态
        String initialEnv = configManager.getCurrentEnvironment();
        Map<String, Object> initialConfig = captureConfigState("初始状态");
        
        // 测试临时配置
        configManager.switchEnvironment(targetEnv, ConfigScope.TEMPORARY);
        Map<String, Object> temporaryConfig = captureConfigState("临时配置");
        
        // 测试全局配置
        configManager.switchEnvironment(targetEnv, ConfigScope.GLOBAL);
        Map<String, Object> globalConfig = captureConfigState("全局配置");
        
        // 清除临时配置
        configManager.clearTemporaryConfig();
        Map<String, Object> afterClearConfig = captureConfigState("清除临时配置后");
        
        response.put("initialEnvironment", initialEnv);
        response.put("targetEnvironment", targetEnv);
        response.put("initialConfig", initialConfig);
        response.put("temporaryConfig", temporaryConfig);
        response.put("globalConfig", globalConfig);
        response.put("afterClearConfig", afterClearConfig);
        response.put("timestamp", System.currentTimeMillis());
        
        logger.info("作用域对比测试完成");
        return ResponseEntity.ok(response);
    }

    /**
     * 测试多线程环境下的临时配置隔离
     */
    @PostMapping("/multi-thread-test")
    public ResponseEntity<Map<String, Object>> multiThreadTest() {
        logger.info("开始多线程临时配置隔离测试");
        
        Map<String, Object> response = new HashMap<>();
        Map<String, CompletableFuture<Map<String, Object>>> futures = new HashMap<>();
        
        // 启动多个线程，每个线程设置不同的临时配置
        String[] environments = {"dev", "prod", "test"};
        
        for (int i = 0; i < environments.length; i++) {
            final String env = environments[i];
            final int threadIndex = i;
            
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(100); // 模拟一些处理时间
                    
                    // 设置临时配置
                    configManager.switchEnvironment(env, ConfigScope.TEMPORARY);
                    
                    // 捕获配置状态
                    Map<String, Object> threadResult = new HashMap<>();
                    threadResult.put("threadId", Thread.currentThread().getId());
                    threadResult.put("threadName", Thread.currentThread().getName());
                    threadResult.put("environment", env);
                    threadResult.put("configSource", configProxy.getConfigSource());
                    threadResult.put("databaseUrl", configProxy.getDatabase().getUrl());
                    threadResult.put("redisHost", configProxy.getRedis().getHost());
                    threadResult.put("apiBaseUrl", configProxy.getApi().getBaseUrl());
                    
                    Thread.sleep(200); // 保持配置一段时间
                    
                    // 清除临时配置
                    configManager.clearTemporaryConfig();
                    
                    return threadResult;
                    
                } catch (Exception e) {
                    logger.error("线程 {} 执行失败", Thread.currentThread().getId(), e);
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("error", e.getMessage());
                    return errorResult;
                }
            }, executorService);
            
            futures.put("thread_" + threadIndex, future);
        }
        
        // 等待所有线程完成
        Map<String, Object> threadResults = new HashMap<>();
        futures.forEach((key, future) -> {
            try {
                threadResults.put(key, future.get());
            } catch (Exception e) {
                logger.error("获取线程结果失败: {}", key, e);
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("error", e.getMessage());
                threadResults.put(key, errorResult);
            }
        });
        
        response.put("threadResults", threadResults);
        response.put("globalEnvironment", configManager.getCurrentEnvironment());
        response.put("temporaryConfigStats", configManager.getTemporaryConfigStatistics());
        response.put("timestamp", System.currentTimeMillis());
        
        logger.info("多线程临时配置隔离测试完成");
        return ResponseEntity.ok(response);
    }

    /**
     * 测试配置代理的智能选择
     */
    @GetMapping("/proxy-test")
    public ResponseEntity<Map<String, Object>> proxyTest() {
        Map<String, Object> response = new HashMap<>();
        
        // 测试全局配置
        Map<String, Object> globalTest = new HashMap<>();
        globalTest.put("configSource", configProxy.getConfigSource());
        globalTest.put("databaseUrl", configProxy.getDatabase().getUrl());
        globalTest.put("isUsingTemporary", configProxy.isUsingTemporaryConfig());
        
        // 设置临时配置
        configManager.switchEnvironment("prod", ConfigScope.TEMPORARY);
        
        Map<String, Object> temporaryTest = new HashMap<>();
        temporaryTest.put("configSource", configProxy.getConfigSource());
        temporaryTest.put("databaseUrl", configProxy.getDatabase().getUrl());
        temporaryTest.put("isUsingTemporary", configProxy.isUsingTemporaryConfig());
        
        // 清除临时配置
        configProxy.clearTemporaryConfig();
        
        Map<String, Object> afterClearTest = new HashMap<>();
        afterClearTest.put("configSource", configProxy.getConfigSource());
        afterClearTest.put("databaseUrl", configProxy.getDatabase().getUrl());
        afterClearTest.put("isUsingTemporary", configProxy.isUsingTemporaryConfig());
        
        response.put("globalTest", globalTest);
        response.put("temporaryTest", temporaryTest);
        response.put("afterClearTest", afterClearTest);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 捕获当前配置状态
     */
    private Map<String, Object> captureConfigState(String description) {
        Map<String, Object> state = new HashMap<>();
        state.put("description", description);
        state.put("threadId", Thread.currentThread().getId());
        state.put("globalEnvironment", configManager.getCurrentEnvironment());
        state.put("hasTemporaryConfig", configManager.hasTemporaryConfig());
        state.put("configSource", configProxy.getConfigSource());
        
        // 配置详情
        Map<String, Object> config = new HashMap<>();
        config.put("databaseUrl", configProxy.getDatabase().getUrl());
        config.put("redisHost", configProxy.getRedis().getHost());
        config.put("apiBaseUrl", configProxy.getApi().getBaseUrl());
        config.put("enableCache", configProxy.getFeature().isEnableCache());
        config.put("enableDebug", configProxy.getFeature().isEnableDebug());
        config.put("enableMonitoring", configProxy.getFeature().isEnableMonitoring());
        
        state.put("config", config);
        state.put("timestamp", System.currentTimeMillis());
        
        return state;
    }
}
