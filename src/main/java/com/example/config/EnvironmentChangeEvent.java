package com.example.config;

import org.springframework.context.ApplicationEvent;

/**
 * 环境切换事件
 * 当环境配置发生切换时发布此事件
 */
public class EnvironmentChangeEvent extends ApplicationEvent {

    private final String oldEnvironment;
    private final String newEnvironment;
    private final long timestamp;

    public EnvironmentChangeEvent(Object source, String oldEnvironment, String newEnvironment) {
        super(source);
        this.oldEnvironment = oldEnvironment;
        this.newEnvironment = newEnvironment;
        this.timestamp = System.currentTimeMillis();
    }

    public String getOldEnvironment() {
        return oldEnvironment;
    }

    public String getNewEnvironment() {
        return newEnvironment;
    }

    public long getEventTimestamp() {
        return timestamp;
    }

    @Override
    public String toString() {
        return "EnvironmentChangeEvent{" +
                "oldEnvironment='" + oldEnvironment + '\'' +
                ", newEnvironment='" + newEnvironment + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
