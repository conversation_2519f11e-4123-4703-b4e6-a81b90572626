package com.example.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.refresh.ContextRefresher;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 动态配置管理器
 * 负责运行时切换不同的配置文件并刷新Spring上下文
 */
@Component
public class DynamicConfigManager {

    private static final Logger logger = LoggerFactory.getLogger(DynamicConfigManager.class);
    
    private static final String DYNAMIC_CONFIG_SOURCE_NAME = "dynamicConfigSource";
    private static final Set<String> SUPPORTED_ENVIRONMENTS = Set.of("dev", "prod", "test");
    
    private final ConfigurableEnvironment environment;
    private final ContextRefresher contextRefresher;
    private final ApplicationEventPublisher eventPublisher;
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    
    private volatile String currentEnvironment = "dev";

    @Autowired
    public DynamicConfigManager(ConfigurableEnvironment environment, 
                               ContextRefresher contextRefresher,
                               ApplicationEventPublisher eventPublisher) {
        this.environment = environment;
        this.contextRefresher = contextRefresher;
        this.eventPublisher = eventPublisher;
        
        // 初始化时加载默认环境配置
        initializeDefaultConfig();
    }

    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfig() {
        try {
            switchEnvironment(currentEnvironment);
            logger.info("初始化默认环境配置: {}", currentEnvironment);
        } catch (Exception e) {
            logger.error("初始化默认配置失败", e);
        }
    }

    /**
     * 切换环境配置
     * 
     * @param targetEnvironment 目标环境
     * @return 切换是否成功
     */
    public boolean switchEnvironment(String targetEnvironment) {
        if (!SUPPORTED_ENVIRONMENTS.contains(targetEnvironment)) {
            logger.warn("不支持的环境: {}，支持的环境: {}", targetEnvironment, SUPPORTED_ENVIRONMENTS);
            return false;
        }

        if (targetEnvironment.equals(currentEnvironment)) {
            logger.info("当前已经是目标环境: {}", targetEnvironment);
            return true;
        }

        lock.writeLock().lock();
        try {
            logger.info("开始切换环境: {} -> {}", currentEnvironment, targetEnvironment);
            
            // 加载新的配置文件
            Properties newProperties = loadConfigProperties(targetEnvironment);
            if (newProperties == null) {
                logger.error("加载配置文件失败: config-{}.properties", targetEnvironment);
                return false;
            }

            // 移除旧的动态配置源
            removeDynamicConfigSource();
            
            // 添加新的配置源
            addDynamicConfigSource(newProperties);
            
            // 刷新Spring上下文
            refreshContext();
            
            // 更新当前环境
            String oldEnvironment = currentEnvironment;
            currentEnvironment = targetEnvironment;
            
            // 发布环境切换事件
            publishEnvironmentChangeEvent(oldEnvironment, targetEnvironment);
            
            logger.info("环境切换成功: {} -> {}", oldEnvironment, targetEnvironment);
            return true;
            
        } catch (Exception e) {
            logger.error("环境切换失败: {} -> {}", currentEnvironment, targetEnvironment, e);
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 加载配置文件属性
     */
    private Properties loadConfigProperties(String env) {
        String configFileName = "config-" + env + ".properties";
        ClassPathResource resource = new ClassPathResource(configFileName);
        
        if (!resource.exists()) {
            logger.error("配置文件不存在: {}", configFileName);
            return null;
        }

        Properties properties = new Properties();
        try {
            properties.load(resource.getInputStream());
            logger.debug("成功加载配置文件: {}，包含 {} 个配置项", configFileName, properties.size());
            return properties;
        } catch (IOException e) {
            logger.error("读取配置文件失败: {}", configFileName, e);
            return null;
        }
    }

    /**
     * 移除动态配置源
     */
    private void removeDynamicConfigSource() {
        if (environment.getPropertySources().contains(DYNAMIC_CONFIG_SOURCE_NAME)) {
            environment.getPropertySources().remove(DYNAMIC_CONFIG_SOURCE_NAME);
            logger.debug("移除旧的动态配置源");
        }
    }

    /**
     * 添加动态配置源
     */
    private void addDynamicConfigSource(Properties properties) {
        PropertiesPropertySource propertySource = new PropertiesPropertySource(
            DYNAMIC_CONFIG_SOURCE_NAME, properties);
        
        // 将动态配置源添加到最高优先级
        environment.getPropertySources().addFirst(propertySource);
        logger.debug("添加新的动态配置源，优先级最高");
    }

    /**
     * 刷新Spring上下文
     */
    private void refreshContext() {
        try {
            Set<String> refreshedKeys = contextRefresher.refresh();
            logger.debug("刷新Spring上下文，更新了 {} 个配置项", refreshedKeys.size());
        } catch (Exception e) {
            logger.error("刷新Spring上下文失败", e);
            throw new RuntimeException("配置刷新失败", e);
        }
    }

    /**
     * 发布环境切换事件
     */
    private void publishEnvironmentChangeEvent(String oldEnv, String newEnv) {
        EnvironmentChangeEvent event = new EnvironmentChangeEvent(this, oldEnv, newEnv);
        eventPublisher.publishEvent(event);
        logger.debug("发布环境切换事件: {} -> {}", oldEnv, newEnv);
    }

    /**
     * 获取当前环境
     */
    public String getCurrentEnvironment() {
        lock.readLock().lock();
        try {
            return currentEnvironment;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取支持的环境列表
     */
    public Set<String> getSupportedEnvironments() {
        return SUPPORTED_ENVIRONMENTS;
    }

    /**
     * 获取当前环境的配置信息
     */
    public Properties getCurrentConfig() {
        lock.readLock().lock();
        try {
            return loadConfigProperties(currentEnvironment);
        } finally {
            lock.readLock().unlock();
        }
    }
}
