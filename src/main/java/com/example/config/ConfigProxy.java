package com.example.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 配置代理类
 * 智能选择返回临时配置或全局配置
 * 优先级：临时配置 > 全局配置
 */
@Component
public class ConfigProxy {

    private static final Logger logger = LoggerFactory.getLogger(ConfigProxy.class);

    private final AppConfig globalConfig;
    private final RequestScopedConfigManager requestScopedConfigManager;

    @Autowired
    public ConfigProxy(AppConfig globalConfig, RequestScopedConfigManager requestScopedConfigManager) {
        this.globalConfig = globalConfig;
        this.requestScopedConfigManager = requestScopedConfigManager;
    }

    /**
     * 获取当前有效的配置实例
     * 优先返回临时配置，如果没有则返回全局配置
     * 
     * @return 当前有效的配置实例
     */
    public AppConfig getCurrentConfig() {
        AppConfig tempConfig = requestScopedConfigManager.getTemporaryConfig();
        
        if (tempConfig != null) {
            logger.debug("返回当前线程的临时配置");
            return tempConfig;
        } else {
            logger.debug("返回全局配置");
            return globalConfig;
        }
    }

    /**
     * 获取数据库配置
     */
    public AppConfig.DatabaseConfig getDatabase() {
        return getCurrentConfig().getDatabase();
    }

    /**
     * 获取Redis配置
     */
    public AppConfig.RedisConfig getRedis() {
        return getCurrentConfig().getRedis();
    }

    /**
     * 获取API配置
     */
    public AppConfig.ApiConfig getApi() {
        return getCurrentConfig().getApi();
    }

    /**
     * 获取功能开关配置
     */
    public AppConfig.FeatureConfig getFeature() {
        return getCurrentConfig().getFeature();
    }

    /**
     * 获取通知配置
     */
    public AppConfig.NotificationConfig getNotification() {
        return getCurrentConfig().getNotification();
    }

    /**
     * 检查当前是否使用临时配置
     */
    public boolean isUsingTemporaryConfig() {
        return requestScopedConfigManager.hasTemporaryConfig();
    }

    /**
     * 获取当前配置的来源信息
     */
    public ConfigSource getConfigSource() {
        if (requestScopedConfigManager.hasTemporaryConfig()) {
            RequestScopedConfigManager.ConfigMetadata metadata = 
                requestScopedConfigManager.getConfigMetadata();
            
            return new ConfigSource(
                ConfigScope.TEMPORARY,
                metadata != null ? metadata.getEnvironment() : "unknown",
                metadata != null ? metadata.getDuration() : 0,
                Thread.currentThread().getId()
            );
        } else {
            return new ConfigSource(
                ConfigScope.GLOBAL,
                "global",
                0,
                Thread.currentThread().getId()
            );
        }
    }

    /**
     * 获取全局配置实例（忽略临时配置）
     */
    public AppConfig getGlobalConfig() {
        return globalConfig;
    }

    /**
     * 获取临时配置实例（如果存在）
     */
    public AppConfig getTemporaryConfig() {
        return requestScopedConfigManager.getTemporaryConfig();
    }

    /**
     * 清除当前线程的临时配置
     */
    public void clearTemporaryConfig() {
        requestScopedConfigManager.clearTemporaryConfig();
    }

    /**
     * 配置来源信息
     */
    public static class ConfigSource {
        private final ConfigScope scope;
        private final String environment;
        private final long duration;
        private final long threadId;

        public ConfigSource(ConfigScope scope, String environment, long duration, long threadId) {
            this.scope = scope;
            this.environment = environment;
            this.duration = duration;
            this.threadId = threadId;
        }

        public ConfigScope getScope() {
            return scope;
        }

        public String getEnvironment() {
            return environment;
        }

        public long getDuration() {
            return duration;
        }

        public long getThreadId() {
            return threadId;
        }

        public boolean isTemporary() {
            return scope.isTemporary();
        }

        public boolean isGlobal() {
            return scope.isGlobal();
        }

        @Override
        public String toString() {
            return String.format("ConfigSource{scope=%s, env='%s', thread=%d, duration=%dms}", 
                               scope, environment, threadId, duration);
        }
    }
}
