package com.example.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 请求级配置管理器
 * 使用ThreadLocal管理临时配置，支持请求级别的配置覆盖
 */
@Component
public class RequestScopedConfigManager {

    private static final Logger logger = LoggerFactory.getLogger(RequestScopedConfigManager.class);

    /**
     * 存储当前线程的临时配置
     * Key: 线程ID, Value: 临时配置实例
     */
    private final ThreadLocal<AppConfig> temporaryConfigHolder = new ThreadLocal<>();
    
    /**
     * 存储临时配置的元数据
     * Key: 线程ID, Value: 配置元数据
     */
    private final Map<Long, ConfigMetadata> configMetadataMap = new ConcurrentHashMap<>();

    /**
     * 设置当前线程的临时配置
     * 
     * @param tempConfig 临时配置实例
     * @param environment 环境名称
     */
    public void setTemporaryConfig(AppConfig tempConfig, String environment) {
        long threadId = Thread.currentThread().getId();
        
        temporaryConfigHolder.set(tempConfig);
        
        ConfigMetadata metadata = new ConfigMetadata(
            environment, 
            System.currentTimeMillis(),
            Thread.currentThread().getName()
        );
        configMetadataMap.put(threadId, metadata);
        
        logger.debug("设置线程 {} 的临时配置: 环境={}", threadId, environment);
    }

    /**
     * 获取当前线程的临时配置
     * 
     * @return 临时配置实例，如果没有则返回null
     */
    public AppConfig getTemporaryConfig() {
        return temporaryConfigHolder.get();
    }

    /**
     * 检查当前线程是否有临时配置
     * 
     * @return true如果有临时配置
     */
    public boolean hasTemporaryConfig() {
        return temporaryConfigHolder.get() != null;
    }

    /**
     * 清除当前线程的临时配置
     */
    public void clearTemporaryConfig() {
        long threadId = Thread.currentThread().getId();
        
        temporaryConfigHolder.remove();
        ConfigMetadata removed = configMetadataMap.remove(threadId);
        
        if (removed != null) {
            logger.debug("清除线程 {} 的临时配置: 环境={}, 存在时长={}ms", 
                        threadId, removed.environment, 
                        System.currentTimeMillis() - removed.createTime);
        }
    }

    /**
     * 获取当前线程的配置元数据
     */
    public ConfigMetadata getConfigMetadata() {
        long threadId = Thread.currentThread().getId();
        return configMetadataMap.get(threadId);
    }

    /**
     * 获取所有活跃的临时配置数量
     */
    public int getActiveTemporaryConfigCount() {
        return configMetadataMap.size();
    }

    /**
     * 清理超时的临时配置
     * 
     * @param timeoutMs 超时时间（毫秒）
     * @return 清理的配置数量
     */
    public int cleanupExpiredConfigs(long timeoutMs) {
        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;
        
        configMetadataMap.entrySet().removeIf(entry -> {
            ConfigMetadata metadata = entry.getValue();
            if (currentTime - metadata.createTime > timeoutMs) {
                logger.debug("清理超时的临时配置: 线程={}, 环境={}, 超时={}ms", 
                           entry.getKey(), metadata.environment, 
                           currentTime - metadata.createTime);
                return true;
            }
            return false;
        });
        
        if (cleanedCount > 0) {
            logger.info("清理了 {} 个超时的临时配置", cleanedCount);
        }
        
        return cleanedCount;
    }

    /**
     * 获取所有临时配置的统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("activeCount", configMetadataMap.size());
        stats.put("currentThreadHasConfig", hasTemporaryConfig());
        
        if (hasTemporaryConfig()) {
            ConfigMetadata metadata = getConfigMetadata();
            if (metadata != null) {
                stats.put("currentThreadEnvironment", metadata.environment);
                stats.put("currentThreadDuration", System.currentTimeMillis() - metadata.createTime);
            }
        }
        
        return stats;
    }

    /**
     * 配置元数据内部类
     */
    public static class ConfigMetadata {
        private final String environment;
        private final long createTime;
        private final String threadName;

        public ConfigMetadata(String environment, long createTime, String threadName) {
            this.environment = environment;
            this.createTime = createTime;
            this.threadName = threadName;
        }

        public String getEnvironment() {
            return environment;
        }

        public long getCreateTime() {
            return createTime;
        }

        public String getThreadName() {
            return threadName;
        }

        public long getDuration() {
            return System.currentTimeMillis() - createTime;
        }

        @Override
        public String toString() {
            return String.format("ConfigMetadata{env='%s', thread='%s', duration=%dms}", 
                               environment, threadName, getDuration());
        }
    }
}
