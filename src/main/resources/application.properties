# Spring Boot Application Configuration
spring.application.name=spring-env-switch

# Server Configuration
server.port=8080

# Default Environment
app.environment=dev

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,refresh
management.endpoint.health.show-details=always

# Logging Configuration
logging.level.com.example=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
