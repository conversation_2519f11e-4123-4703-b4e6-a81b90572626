# Test Environment Configuration
app.database.url=jdbc:h2:mem:testdb
app.database.username=sa
app.database.password=
app.database.pool.max-size=5

app.redis.host=localhost
app.redis.port=6379
app.redis.database=2

app.api.base-url=https://test-api.example.com
app.api.timeout=3000
app.api.retry-count=2

app.feature.enable-cache=false
app.feature.enable-debug=true
app.feature.enable-monitoring=false

app.notification.email.enabled=false
app.notification.sms.enabled=false
