# Production Environment Configuration
app.database.url=*********************************************
app.database.username=prod_user
app.database.password=prod_secure_password
app.database.pool.max-size=50

app.redis.host=prod-redis.example.com
app.redis.port=6379
app.redis.database=1

app.api.base-url=https://api.example.com
app.api.timeout=10000
app.api.retry-count=5

app.feature.enable-cache=true
app.feature.enable-debug=false
app.feature.enable-monitoring=true

app.notification.email.enabled=true
app.notification.sms.enabled=true
